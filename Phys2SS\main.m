%% test model
%   2025-08-06
clc;clear;close all;

%% 导入文件
filename = "C612save1.csv";
fid = fopen(filename);
d = textscan(fid,"%D%d%d%d%d%d%d%q%q%q","Delimiter",',',"HeaderLines",1);
fclose(fid);

Mtime = d{1,1};
Mpattern = d{1,2};
Mspeed = double(d{1,3});
Mspeed = Mspeed/60;
MTen = double(d{1,4});
MTex_mea = double(d{1,5});
MTex_spt = double(d{1,6});
Mhd = parseT(d{1,8});
Mtt = parseT(d{1,9});
[coils0,coils1] = parseCoil(d{1,10});

% plot data
% figure;
% plot(Mtime,MTen,"b-",Mtime,MTex_mea,"r-",Mtime,MTex_spt,"r--");
%% 结构参数
RTFLenth = 552; % RTF段总长
Nx = 25;    % 24列带钢+入口温度
% Nt = length(MTen);
Nt = 20000;
dx = 23;    % 一列带钢长度

dt = 2; % 时间步 - 减小以提高数值稳定性
%% 工况参数


% 定义系数
C = funC();

%% 物性参数
% strip
rhos = 7850;
global CPDATA
tmp = importdata("steelprop.csv");% 带钢比热插值数据 J/kg/K
CPDATA = tmp.data;

% burner
global CPHEAT BURNPOW
CPHEAT = [20,510;200,421;400,536;600,563;800,580;1000,593]; % 辐射管比热插值数据
BURNPOW = importdata("BURNPOW.mat");    % num  pow
burnerPow = 1000*BURNPOW(:,1).*BURNPOW(:,2)*0.01*0.35;   % 单位kW, hd-%  0.35是修正系数
%% 初始化矩阵


Xs0 = linspace(MTen(1),MTex_mea(1),Nx);
Xs0 = Xs0';
Xh0 = Mtt(1,:)';
X0 = [Xs0;Xh0];

A11 = zeros(Nx,Nx);
A12 = A11;
A21 = A11;
A22 = A11;

A11(1,1) = 1;
Bm = zeros(2*Nx,Nx);

xx = (1:25)';
Tout = zeros(2,Nt);

%% 时间循环
Tplot = 1000;
for jt = 1:Nt
    % 获取参数
    vel = Mspeed(jt);
    
    % 当前的带钢信息
    coildata = [coils0(jt).thick*0.001,coils0(jt).width*0.001,coils0(jt).weldPercent;
        coils1(jt).thick*0.001,coils1(jt).width*0.001,coils1(jt).weldPercent];
    % A11
    beta = dt*vel/dx;
    % 计算矩阵系数
    [gamma,aij,aji,bjj] = def_gamma(C,X0,coildata,dt,dx);
    for i = 2:25
        A11(i,i) = 1- beta - gamma(i-1);
        A11(i,i-1) = beta;
    end
    A12(2:25,:) = aij;
    A21(:,2:25) = aji;
    for i = 1:25
        A22(i,i) = 1- sum(aji(i,:));
        Bm(i+Nx,i) = bjj(i);
    end
    % 组装矩阵
    A = [A11,A12;A21,A22];

    X0(1) = MTen(jt);
    X0(26:end) = Mtt(jt,:)';
    U = burnerPow.*Mhd(jt,:)';
    
    Xnew = A*X0 + Bm*U;
    Xsnew = Xnew(1:25);
    Xhnew = Xnew(26:end);

    % 数值稳定性检查和温度变化限制
    max_temp_change = 50; % 最大温度变化限制 (°C)

    % 检查带钢温度变化
    temp_change_s = abs(Xsnew - X0(1:25));
    if any(temp_change_s > max_temp_change)
        warning('Large temperature change detected in strip at time %d', jt);
        % 限制温度变化幅度
        idx_large = temp_change_s > max_temp_change;
        sign_change = sign(Xsnew - X0(1:25));
        Xsnew(idx_large) = X0(idx_large) + sign_change(idx_large) * max_temp_change;
    end

    % 检查辐射管温度变化
    temp_change_h = abs(Xhnew - X0(26:end));
    if any(temp_change_h > max_temp_change)
        warning('Large temperature change detected in heater at time %d', jt);
        % 限制温度变化幅度
        idx_large = temp_change_h > max_temp_change;
        sign_change = sign(Xhnew - X0(26:end));
        Xhnew(idx_large) = X0(26+idx_large-1) + sign_change(idx_large) * max_temp_change;
    end

    % 更新Xnew
    Xnew = [Xsnew; Xhnew];

    Tout(1,jt) = Xnew(25);
    Tout(2,jt) = MTex_mea(jt);

    % 改进的错误检查
    if any(isnan(Xsnew)) || any(isinf(Xsnew))
        error('NaN or Inf detected in strip temperature at time %d', jt);
    end
    if any(isnan(Xhnew)) || any(isinf(Xhnew))
        error('NaN or Inf detected in heater temperature at time %d', jt);
    end
    if mod(jt,Tplot)==0
    figure(1)
    plot(xx,Xsnew,'ro-',xx,Xhnew,'bx-',"LineWidth",2);
    hold on
    plot([1,25],[MTen(jt),MTex_mea(jt)],'^-',"LineWidth",1);
    hold on
    plot(xx,Mtt(jt,:)','v-',"LineWidth",1);
    hold off
    xlabel("Location");
    ylabel('Temperature');
    legend('Strip','Heater','S-mea','RT-mea',"Location","best");
    set(gca,'FontSize',14);
    title(sprintf('%d,d=%.2f',jt,coils0(jt).thick));
%     pause(0.5)
    end

    X0 = Xnew;
end
figure(2)
tt = 1:Nt;
plot(tt,Tout(1,:),'r-',tt,Tout(2,:),'k--',"LineWidth",1);
xlabel("Time");
ylabel('Temperature');
legend('Trtf-ex-sim','Trtf-ex-mea',"Location","best");
set(gca,'FontSize',14);
