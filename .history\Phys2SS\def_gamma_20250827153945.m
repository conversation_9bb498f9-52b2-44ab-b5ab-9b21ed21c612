function [gamma,aij,aji,bjj] = def_gamma(C,X0,coildata,dt,dx)
%DEF_GAMMA 定义矩阵A组装的系数
%   此处显示详细说明
% 552 = 24*23
mheat0 = 700;   % 单个辐射管的质量,kg

global BURNPOW  % 辐射管 列数 功率
mj = mheat0*BURNPOW(:,1);   % 一列辐射管总质量，kg
sigma = 5.67e-8;
Nx = 25;
Xs0 = X0(1:Nx);         % X strip
Xh0 = X0(Nx+1:end);     % X heater

Lheat = 19;     % 加热面长度

weld_pre = coildata(2,3);   % 后行卷剩余比例
idx = floor(weld_pre/(100/24));
tmp = rem(weld_pre,100/24);
percen = zeros(24,1);   % 24列每一列前后卷的占比
if weld_pre>0
    percen(1:idx) = 1;
    percen(idx+1) = tmp/100;
end
d = coildata(2,1)*percen + (1-percen)*coildata(1,1);    % 24列带钢的厚度分布 如果是两卷的话
b = coildata(2,2)*percen + (1-percen)*coildata(1,2);    % 24列带钢的宽度分布

rho = 7850;
cpsAll = fCps(Xs0);
cps = cpsAll(2:end);

gamma = zeros(24,1);
aij = zeros(24,25);

cph = fCps(Xh0);
aji = zeros(25,24);
% 计算每一列的系数
for i = 1:24
    tmp1 = 1/cps(i)/rho/d(i)*dt/dx*Lheat;
    tmp2 = solveh(Xs0(i),Xh0(i));
    tmp3 = solveh(Xs0(i),Xh0(i+1));
    h1 = sigma*C(i)*tmp2;
    h2 = sigma*C(i+1)*tmp3;

    gamma(i) = tmp1*(h1+h2);

    aij(i,i) = tmp1*h1;
    aij(i,i+1) = tmp1*h2;

    aji(i,i) = dt/mj(i)/cph(i)*h1*Lheat*b(i);
    aji(i+1,i) = dt/mj(i+1)/cph(i+1)*h2*Lheat*b(i);
end
bjj = dt./mj./cph;

end
function fh = solveh(x,y)
% 计算辐射换热系数
% h = (T1^4-T2^4)/(T1-T2)
a = x + 273;
b = y + 273;

% 添加温度差异检查，避免极端情况
temp_diff = abs(a - b);
if temp_diff < 1e-6
    % 温度差很小时，使用线性近似
    fh = 4 * ((a + b)/2)^3;
else
    % 正常情况下的辐射换热系数
    fh = ((a)^2+(b)^2)*(a+b);
end

% 限制换热系数的最大值，避免数值问题
max_h = 1e6;
if fh > max_h
    fh = max_h;
    warning('Radiation heat transfer coefficient limited to maximum value');
end

% 确保换热系数为正值
fh = abs(fh);
end
